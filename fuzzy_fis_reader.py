"""
Python implementation to read and evaluate .fis files
This provides basic functionality similar to MATLAB's readfis and evalfis
"""

import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Any
import re

class FISReader:
    def __init__(self):
        self.system = {}
        self.inputs = []
        self.outputs = []
        self.rules = []
    
    def read_fis(self, filename: str) -> Dict:
        """Read a .fis file and parse its contents"""
        with open(filename, 'r') as file:
            content = file.read()
        
        # Parse system section
        system_match = re.search(r'\[System\](.*?)(?=\[|$)', content, re.DOTALL)
        if system_match:
            self._parse_system(system_match.group(1))
        
        # Parse inputs
        input_matches = re.findall(r'\[Input(\d+)\](.*?)(?=\[|$)', content, re.DOTALL)
        for input_num, input_content in input_matches:
            self._parse_input(int(input_num), input_content)
        
        # Parse outputs
        output_matches = re.findall(r'\[Output(\d+)\](.*?)(?=\[|$)', content, re.DOTALL)
        for output_num, output_content in output_matches:
            self._parse_output(int(output_num), output_content)
        
        # Parse rules
        rules_match = re.search(r'\[Rules\](.*?)(?=\[|$)', content, re.DOTALL)
        if rules_match:
            self._parse_rules(rules_match.group(1))
        
        return {
            'system': self.system,
            'inputs': self.inputs,
            'outputs': self.outputs,
            'rules': self.rules
        }
    
    def _parse_system(self, content: str):
        """Parse system section"""
        lines = content.strip().split('\n')
        for line in lines:
            if '=' in line:
                key, value = line.split('=', 1)
                self.system[key.strip()] = value.strip().strip("'")
    
    def _parse_input(self, input_num: int, content: str):
        """Parse input section"""
        lines = content.strip().split('\n')
        input_data = {'number': input_num}
        
        for line in lines:
            if '=' in line:
                key, value = line.split('=', 1)
                key = key.strip()
                value = value.strip().strip("'")
                
                if key == 'Range':
                    # Parse range [min max]
                    range_match = re.search(r'\[(.*?)\]', value)
                    if range_match:
                        input_data['range'] = [float(x) for x in range_match.group(1).split()]
                elif key.startswith('MF'):
                    # Parse membership function
                    if 'mfs' not in input_data:
                        input_data['mfs'] = []
                    
                    # Extract MF details: 'name':'type',[params]
                    mf_match = re.search(r"'(.*?)':'(.*?)',\[(.*?)\]", value)
                    if mf_match:
                        mf_name, mf_type, mf_params = mf_match.groups()
                        params = [float(x) for x in mf_params.split(',')]
                        input_data['mfs'].append({
                            'name': mf_name,
                            'type': mf_type,
                            'params': params
                        })
                else:
                    input_data[key.lower()] = value
        
        # Ensure inputs list is large enough
        while len(self.inputs) < input_num:
            self.inputs.append({})
        self.inputs[input_num - 1] = input_data
    
    def _parse_output(self, output_num: int, content: str):
        """Parse output section"""
        lines = content.strip().split('\n')
        output_data = {'number': output_num}
        
        for line in lines:
            if '=' in line:
                key, value = line.split('=', 1)
                key = key.strip()
                value = value.strip().strip("'")
                
                if key == 'Range':
                    # Parse range [min max]
                    range_match = re.search(r'\[(.*?)\]', value)
                    if range_match:
                        output_data['range'] = [float(x) for x in range_match.group(1).split()]
                elif key.startswith('MF'):
                    # Parse membership function
                    if 'mfs' not in output_data:
                        output_data['mfs'] = []
                    
                    # Extract MF details: 'name':'type',[params]
                    mf_match = re.search(r"'(.*?)':'(.*?)',\[(.*?)\]", value)
                    if mf_match:
                        mf_name, mf_type, mf_params = mf_match.groups()
                        params = [float(x) for x in mf_params.split(',')]
                        output_data['mfs'].append({
                            'name': mf_name,
                            'type': mf_type,
                            'params': params
                        })
                else:
                    output_data[key.lower()] = value
        
        # Ensure outputs list is large enough
        while len(self.outputs) < output_num:
            self.outputs.append({})
        self.outputs[output_num - 1] = output_data
    
    def _parse_rules(self, content: str):
        """Parse rules section"""
        lines = content.strip().split('\n')
        for line in lines:
            line = line.strip()
            if line and not line.startswith('%'):
                # Parse rule format: input1 input2 input3, output1 (weight) : method
                parts = line.split(',')
                if len(parts) >= 2:
                    inputs = [int(x) for x in parts[0].strip().split()]
                    output_part = parts[1].strip()
                    
                    # Extract output and weight
                    output_match = re.search(r'(\d+)\s*\(([^)]+)\)', output_part)
                    if output_match:
                        output = int(output_match.group(1))
                        weight = float(output_match.group(2))
                        
                        self.rules.append({
                            'inputs': inputs,
                            'output': output,
                            'weight': weight
                        })

def gaussian_mf(x, params):
    """Gaussian membership function"""
    sigma, c = params[0], params[1]
    return np.exp(-0.5 * ((x - c) / sigma) ** 2)

def trapezoidal_mf(x, params):
    """Trapezoidal membership function"""
    a, b, c, d = params
    return np.maximum(0, np.minimum(1, np.minimum((x - a) / (b - a), (d - x) / (d - c))))

def evaluate_mf(x, mf_type, params):
    """Evaluate membership function"""
    if mf_type == 'gaussmf':
        return gaussian_mf(x, params)
    elif mf_type == 'trapmf':
        return trapezoidal_mf(x, params)
    else:
        raise ValueError(f"Unsupported membership function type: {mf_type}")

def evalfis(inputs: List[float], fis_data: Dict) -> float:
    """Evaluate fuzzy inference system"""
    # Get input membership degrees
    input_memberships = []
    for i, input_val in enumerate(inputs):
        input_data = fis_data['inputs'][i]
        mf_values = []
        for mf in input_data['mfs']:
            membership = evaluate_mf(input_val, mf['type'], mf['params'])
            mf_values.append(membership)
        input_memberships.append(mf_values)
    
    # Evaluate rules
    output_data = fis_data['outputs'][0]  # Assuming single output
    output_range = output_data['range']
    x_output = np.linspace(output_range[0], output_range[1], 1000)
    
    aggregated_output = np.zeros_like(x_output)
    
    for rule in fis_data['rules']:
        # Calculate rule strength (minimum of input memberships)
        rule_strength = 1.0
        for i, input_mf_idx in enumerate(rule['inputs']):
            if input_mf_idx > 0:  # 0 means don't care
                rule_strength = min(rule_strength, input_memberships[i][input_mf_idx - 1])
        
        # Apply rule weight
        rule_strength *= rule['weight']
        
        # Get output membership function
        output_mf_idx = rule['output'] - 1
        output_mf = output_data['mfs'][output_mf_idx]
        
        # Calculate output membership
        output_membership = evaluate_mf(x_output, output_mf['type'], output_mf['params'])
        
        # Apply implication (minimum)
        implied_output = np.minimum(rule_strength, output_membership)
        
        # Aggregate (maximum)
        aggregated_output = np.maximum(aggregated_output, implied_output)
    
    # Defuzzification (centroid method)
    if np.sum(aggregated_output) == 0:
        return (output_range[0] + output_range[1]) / 2  # Return middle value if no rules fire
    
    centroid = np.sum(x_output * aggregated_output) / np.sum(aggregated_output)
    return centroid

# Example usage
if __name__ == "__main__":
    # Test with your files
    fis_reader = FISReader()
    
    # Read the trapezoidal FIS
    fis_data = fis_reader.read_fis('Q4a.fis')
    
    # Test inputs: [CreditHistory, Age, Salary]
    test_inputs = [50, 35, 100]
    result = evalfis(test_inputs, fis_data)
    
    print(f"Input: Credit History={test_inputs[0]}, Age={test_inputs[1]}, Salary={test_inputs[2]}")
    print(f"Trapezoidal FIS Output (Risk Level): {result:.2f}")
    
    # Also test with Gaussian FIS
    fis_data_gauss = fis_reader.read_fis('Q2.fis')
    result_gauss = evalfis(test_inputs, fis_data_gauss)
    print(f"Gaussian FIS Output (Risk Level): {result_gauss:.2f}")
