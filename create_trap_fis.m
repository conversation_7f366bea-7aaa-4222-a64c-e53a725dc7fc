% Create trapezoidal FIS programmatically
fis_trap = mamfis('Name', 'CreditRiskAssessment_Trap');

% Add inputs
fis_trap = addInput(fis_trap, [0 100], 'Name', 'CreditHistory');
fis_trap = addMF(fis_trap, 'CreditHistory', 'trapmf', [0 0 25 40], 'Name', 'Poor');
fis_trap = addMF(fis_trap, 'CreditHistory', 'trapmf', [30 40 60 70], 'Name', 'Average');
fis_trap = addMF(fis_trap, 'CreditHistory', 'trapmf', [60 75 100 100], 'Name', 'Excellent');

fis_trap = addInput(fis_trap, [18 80], 'Name', 'Age');
fis_trap = addMF(fis_trap, 'Age', 'trapmf', [18 18 30 35], 'Name', 'Young');
fis_trap = addMF(fis_trap, 'Age', 'trapmf', [30 40 50 60], 'Name', 'MiddleAge');
fis_trap = addMF(fis_trap, 'Age', 'trapmf', [55 65 80 80], 'Name', 'Senior');

fis_trap = addInput(fis_trap, [0 200], 'Name', 'Salary');
fis_trap = addMF(fis_trap, 'Salary', 'trapmf', [0 0 40 60], 'Name', 'Low');
fis_trap = addMF(fis_trap, 'Salary', 'trapmf', [50 80 120 140], 'Name', 'Medium');
fis_trap = addMF(fis_trap, 'Salary', 'trapmf', [130 160 200 200], 'Name', 'High');

% Add output
fis_trap = addOutput(fis_trap, [0 100], 'Name', 'RiskLevel');
fis_trap = addMF(fis_trap, 'RiskLevel', 'trapmf', [0 0 25 35], 'Name', 'LowRisk');
fis_trap = addMF(fis_trap, 'RiskLevel', 'trapmf', [30 45 55 70], 'Name', 'MediumRisk');
fis_trap = addMF(fis_trap, 'RiskLevel', 'trapmf', [65 75 100 100], 'Name', 'HighRisk');

% Add rules
rules = [
    "CreditHistory==Poor & Age==Young & Salary==Low => RiskLevel=HighRisk";
    "CreditHistory==Poor & Age==Young & Salary==Medium => RiskLevel=HighRisk";
    "CreditHistory==Poor & Age==Young & Salary==High => RiskLevel=MediumRisk";
    % Add more rules as needed
];

fis_trap = addRule(fis_trap, rules);

% Test evaluation
test_inputs = [50, 35, 100];
trap_output = evalfis(fis_trap, test_inputs)

% Save the FIS
writeFIS(fis_trap, 'Q4a_programmatic.fis');
