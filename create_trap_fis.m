% Load fuzzy logic toolkit
pkg load fuzzy-logic-toolkit

% Test evaluation with existing trapezoidal FIS file
fprintf('Loading trapezoidal FIS from Q4a.fis...\n');
fis_trap = readfis('Q4a.fis');

% Display some information about the FIS
fprintf('FIS Name: %s\n', fis_trap.name);
fprintf('FIS Type: %s\n', fis_trap.type);
fprintf('Number of inputs: %d\n', length(fis_trap.input));
fprintf('Number of outputs: %d\n', length(fis_trap.output));

% Test inputs: [CreditHistory, Age, Salary]
test_inputs = [50, 35, 100];
fprintf('Test inputs: Credit History=%.0f, Age=%.0f, Salary=%.0f\n', test_inputs(1), test_inputs(2), test_inputs(3));

% Try to evaluate the FIS
try
    trap_output = evalfis(test_inputs, fis_trap);
    fprintf('Trapezoidal output: %.2f\n', trap_output);
catch err
    fprintf('Error evaluating FIS: %s\n', err.message);
    fprintf('Attempting manual evaluation...\n');

    % Manual evaluation of the trapezoidal FIS
    % Calculate membership degrees for each input
    input_memberships = [];

    for i = 1:length(fis_trap.input)
        input_val = test_inputs(i);
        mf_values = [];

        for j = 1:length(fis_trap.input(i).mf)
            mf = fis_trap.input(i).mf(j);
            if strcmp(mf.type, 'trapmf')
                % Manual trapezoidal membership function
                params = mf.params;
                a = params(1); b = params(2); c = params(3); d = params(4);

                if input_val <= a || input_val >= d
                    membership = 0;
                elseif input_val >= b && input_val <= c
                    membership = 1;
                elseif input_val > a && input_val < b
                    membership = (input_val - a) / (b - a);
                else % input_val > c && input_val < d
                    membership = (d - input_val) / (d - c);
                end

                mf_values = [mf_values, membership];
                fprintf('  Input %d (%s), MF %d (%s): %.3f\n', i, fis_trap.input(i).name, j, mf.name, membership);
            end
        end
        input_memberships = [input_memberships; mf_values];
    end

    % Simple rule evaluation (using first few rules as example)
    % This is a simplified version - full implementation would need all 27 rules
    fprintf('\nSimplified rule evaluation:\n');

    % Example: If CreditHistory is Average AND Age is Young AND Salary is Medium, then Risk is Medium
    % Rule indices: CreditHistory(2), Age(1), Salary(2) -> Risk(2)
    rule_strength = min([input_memberships(1,2), input_memberships(2,1), input_memberships(3,2)]);
    fprintf('Example rule strength: %.3f\n', rule_strength);

    % For a complete evaluation, we'd need to implement all rules and defuzzification
    fprintf('Manual evaluation would require implementing all 27 rules and defuzzification.\n');
    fprintf('The Gaussian FIS works correctly and gives output: ');

    % Test Gaussian FIS for comparison
    fis_gauss = readfis('Q2.fis');
    gauss_output = evalfis(test_inputs, fis_gauss);
    fprintf('%.2f\n', gauss_output);
end

