#!/usr/bin/env python3
"""
Python equivalent of create_trap_fis.m
Tests the trapezoidal fuzzy inference system
"""

from fuzzy_fis_reader import <PERSON><PERSON><PERSON><PERSON><PERSON>, evalfis

def main():
    print("Testing Fuzzy Logic System with Python")
    print("=" * 50)
    
    # Create FIS reader
    fis_reader = FISReader()
    
    try:
        # Read the trapezoidal FIS file (equivalent to readfis('Q4a.fis'))
        print("Loading trapezoidal FIS from Q4a.fis...")
        fis_trap = fis_reader.read_fis('Q4a.fis')
        
        # Display system information
        print(f"System Name: {fis_trap['system'].get('Name', 'Unknown')}")
        print(f"System Type: {fis_trap['system'].get('Type', 'Unknown')}")
        print(f"Number of Inputs: {len(fis_trap['inputs'])}")
        print(f"Number of Outputs: {len(fis_trap['outputs'])}")
        print(f"Number of Rules: {len(fis_trap['rules'])}")
        print()
        
        # Display input information
        for i, input_data in enumerate(fis_trap['inputs']):
            print(f"Input {i+1}: {input_data.get('name', f'Input{i+1}')}")
            print(f"  Range: {input_data.get('range', 'Unknown')}")
            print(f"  Membership Functions:")
            for mf in input_data.get('mfs', []):
                print(f"    - {mf['name']}: {mf['type']} {mf['params']}")
            print()
        
        # Test evaluation with the same inputs as in your MATLAB script
        test_inputs = [50, 35, 100]  # [CreditHistory, Age, Salary]
        print(f"Test Inputs: Credit History={test_inputs[0]}, Age={test_inputs[1]}, Salary={test_inputs[2]}")
        
        # Evaluate the fuzzy system (equivalent to evalfis)
        trap_output = evalfis(test_inputs, fis_trap)
        
        print(f"Trapezoidal output: {trap_output:.2f}")
        print()
        
        # Also test with the Gaussian FIS for comparison
        print("Loading Gaussian FIS from Q2.fis for comparison...")
        fis_gauss = fis_reader.read_fis('Q2.fis')
        gauss_output = evalfis(test_inputs, fis_gauss)
        print(f"Gaussian output: {gauss_output:.2f}")
        
        print()
        print("Comparison:")
        print(f"Trapezoidal FIS: {trap_output:.2f}")
        print(f"Gaussian FIS:    {gauss_output:.2f}")
        print(f"Difference:      {abs(trap_output - gauss_output):.2f}")
        
    except FileNotFoundError as e:
        print(f"Error: Could not find file {e.filename}")
        print("Make sure Q4a.fis and Q2.fis are in the current directory")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
