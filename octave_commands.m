% Load both FIS files
fis_gauss = readfis('C:\Users\<USER>\Documents\Projects\assignment\ai_assignment\Q2.fis');
fis_trap = readfis('C:\Users\<USER>\Documents\Projects\assignment\ai_assignment\Q4a.fis');

% Test inputs for comparison
test_inputs = [50, 35, 100];

% Evaluate both systems
gauss_output = evalfis(test_inputs, fis_gauss);
trap_output = evalfis(test_inputs, fis_trap);

% Now display results
fprintf('Same input [50,35,100]:\n');
fprintf('Gaussian output: %.2f\n', gauss_output);
fprintf('Trapezoidal output: %.2f\n', trap_output);
fprintf('Difference: %.2f\n', abs(gauss_output - trap_output));