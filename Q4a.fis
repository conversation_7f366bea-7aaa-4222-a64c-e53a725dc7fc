[System]
Name='CreditRiskAssessment_Trapezoidal'
Type='mamdani'
Version=2.0
NumInputs=3
NumOutputs=1
NumRules=27
AndMethod='min'
OrMethod='max'
ImpMethod='min'
AggMethod='max'
DefuzzMethod='centroid'

[Input1]
Name='CreditHistory'
Range=[0 100]
NumMFs=3
MF1='Poor':'trapmf',[0,0,25,40]
MF2='Average':'trapmf',[30,40,60,70]
MF3='Excellent':'trapmf',[60,75,100,100]

[Input2]
Name='Age'
Range=[18 80]
NumMFs=3
MF1='Young':'trapmf',[18,18,30,35]
MF2='MiddleAge':'trapmf',[30,40,50,60]
MF3='Senior':'trapmf',[55,65,80,80]

[Input3]
Name='Salary'
Range=[0 200]
NumMFs=3
MF1='Low':'trapmf',[0,0,40,60]
MF2='Medium':'trapmf',[50,80,120,140]
MF3='High':'trapmf',[130,160,200,200]

[Output1]
Name='RiskLevel'
Range=[0 100]
NumMFs=3
MF1='LowRisk':'trapmf',[0,0,25,35]
MF2='MediumRisk':'trapmf',[30,45,55,70]
MF3='HighRisk':'trapmf',[65,75,100,100]

[Rules]
1 1 1, 3 (1) : 1
1 1 2, 3 (1) : 1
1 1 3, 2 (1) : 1
1 2 1, 3 (1) : 1
1 2 2, 2 (1) : 1
1 2 3, 2 (1) : 1
1 3 1, 2 (1) : 1
1 3 2, 2 (1) : 1
1 3 3, 1 (1) : 1
2 1 1, 3 (1) : 1
2 1 2, 2 (1) : 1
2 1 3, 2 (1) : 1
2 2 1, 2 (1) : 1
2 2 2, 2 (1) : 1
2 2 3, 1 (1) : 1
2 3 1, 2 (1) : 1
2 3 2, 1 (1) : 1
2 3 3, 1 (1) : 1
3 1 1, 2 (1) : 1
3 1 2, 2 (1) : 1
3 1 3, 1 (1) : 1
3 2 1, 2 (1) : 1
3 2 2, 1 (1) : 1
3 2 3, 1 (1) : 1
3 3 1, 1 (1) : 1
3 3 2, 1 (1) : 1
3 3 3, 1 (1) : 1




