@echo off
"C:\Program Files\GNU Octave\Octave-10.2.0\mingw64\bin\octave.exe" --eval "pkg load fuzzy-logic-toolkit; fprintf('Testing Gaussian FIS...\n'); fis_gauss = readfis('Q2.fis'); test_inputs = [50, 35, 100]; result_gauss = evalfis(test_inputs, fis_gauss); disp(['Gaussian output: ' num2str(result_gauss)]); fprintf('Testing Trapezoidal FIS...\n'); fis_trap = readfis('Q4a.fis'); try; result_trap = evalfis(test_inputs, fis_trap); disp(['Trapezoidal output: ' num2str(result_trap)]); catch err; disp(['Error: ' err.message]); end"
