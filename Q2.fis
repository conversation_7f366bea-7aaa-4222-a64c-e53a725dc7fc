[System]
Name='CreditRiskAssessment'
Type='mamdani'
Version=2.0
NumInputs=3
NumOutputs=1
NumRules=27
AndMethod='min'
OrMethod='max'
ImpMethod='min'
AggMethod='max'
DefuzzMethod='centroid'

[Input1]
Name='CreditHistory'
Range=[0 100]
NumMFs=3
MF1='Poor':'gaussmf',[15 0]
MF2='Average':'gaussmf',[15 50]
MF3='Excellent':'gaussmf',[15 100]

[Input2]
Name='Age'
Range=[18 80]
NumMFs=3
MF1='Young':'gaussmf',[10 25]
MF2='MiddleAge':'gaussmf',[10 45]
MF3='Senior':'gaussmf',[10 65]

[Input3]
Name='Salary'
Range=[0 200]
NumMFs=3
MF1='Low':'gaussmf',[30 25]
MF2='Medium':'gaussmf',[30 100]
MF3='High':'gaussmf',[30 175]

[Output1]
Name='RiskLevel'
Range=[0 100]
NumMFs=3
MF1='LowRisk':'gaussmf',[15 20]
MF2='MediumRisk':'gaussmf',[15 50]
MF3='HighRisk':'gaussmf',[15 80]

[Rules]
1 1 1, 3 (1) : 1
1 1 2, 3 (1) : 1
1 1 3, 2 (1) : 1
1 2 1, 3 (1) : 1
1 2 2, 2 (1) : 1
1 2 3, 2 (1) : 1
1 3 1, 2 (1) : 1
1 3 2, 2 (1) : 1
1 3 3, 1 (1) : 1
2 1 1, 3 (1) : 1
2 1 2, 2 (1) : 1
2 1 3, 2 (1) : 1
2 2 1, 2 (1) : 1
2 2 2, 2 (1) : 1
2 2 3, 1 (1) : 1
2 3 1, 2 (1) : 1
2 3 2, 1 (1) : 1
2 3 3, 1 (1) : 1
3 1 1, 2 (1) : 1
3 1 2, 2 (1) : 1
3 1 3, 1 (1) : 1
3 2 1, 2 (1) : 1
3 2 2, 1 (1) : 1
3 2 3, 1 (1) : 1
3 3 1, 1 (1) : 1
3 3 2, 1 (1) : 1
3 3 3, 1 (1) : 1